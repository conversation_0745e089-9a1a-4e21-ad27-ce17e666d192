@echo off
echo 🚀 Restarting Shopify App Development Server...
echo.

echo 📋 Step 1: Stopping any existing processes...
taskkill /f /im node.exe 2>nul
timeout /t 2 /nobreak >nul

echo 📋 Step 2: Starting development server...
echo.
echo ⚠️  IMPORTANT: Look for the new tunnel URL in the output below!
echo    It will look like: https://some-words.trycloudflare.com
echo.

npm run dev

echo.
echo 📋 If the server started successfully:
echo 1. Copy the new tunnel URL from above
echo 2. Update your shopify.app.toml file if needed
echo 3. Test your extension again
echo.
pause
