# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "dd4825d915f4a7b2e50cedd0d8506232"
name = "logistic-app-new"
handle = "logistic-app-new"
application_url = "https://either-wife-deals-hazardous.trycloudflare.com"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-07"
 [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"


[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_orders,write_products"

[auth]
redirect_urls = ["https://either-wife-deals-hazardous.trycloudflare.com/auth/callback", "https://either-wife-deals-hazardous.trycloudflare.com/auth/shopify/callback", "https://either-wife-deals-hazardous.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false
