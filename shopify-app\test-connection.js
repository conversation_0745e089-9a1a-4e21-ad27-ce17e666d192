// Simple test script to verify your app is running
// Run with: node test-connection.js

const https = require('https');
const http = require('http');

const testUrls = [
  'https://enjoyed-italian-drove-antique.trycloudflare.com/api/health',
  'http://localhost:3000/api/health',
  'https://localhost:3000/api/health',
];

async function testConnection(url) {
  return new Promise((resolve) => {
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        resolve({
          url,
          status: res.statusCode,
          success: res.statusCode === 200,
          data: data.substring(0, 200) + (data.length > 200 ? '...' : '')
        });
      });
    });
    
    req.on('error', (error) => {
      resolve({
        url,
        success: false,
        error: error.message
      });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        url,
        success: false,
        error: 'Timeout'
      });
    });
  });
}

async function runTests() {
  console.log('🧪 Testing connection to your Shopify app...\n');
  
  for (const url of testUrls) {
    console.log(`Testing: ${url}`);
    const result = await testConnection(url);
    
    if (result.success) {
      console.log('✅ SUCCESS:', result.status);
      console.log('📄 Response:', result.data);
      console.log('🎉 Your app is accessible at:', url);
      break;
    } else {
      console.log('❌ FAILED:', result.error || result.status);
    }
    console.log('');
  }
  
  console.log('\n📋 Next steps:');
  console.log('1. Make sure your development server is running: npm run dev');
  console.log('2. Check the tunnel URL in your terminal output');
  console.log('3. Update the extension if the tunnel URL changed');
  console.log('4. Test order processing with the working URL');
}

runTests().catch(console.error);
