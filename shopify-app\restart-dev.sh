#!/bin/bash

echo "🚀 Restarting Shopify App Development Server..."
echo ""

echo "📋 Step 1: Stopping any existing processes..."
pkill -f "npm run dev" 2>/dev/null || true
pkill -f "shopify app dev" 2>/dev/null || true
sleep 2

echo "📋 Step 2: Starting development server..."
echo ""
echo "⚠️  IMPORTANT: Look for the new tunnel URL in the output below!"
echo "   It will look like: https://some-words.trycloudflare.com"
echo ""

npm run dev

echo ""
echo "📋 If the server started successfully:"
echo "1. Copy the new tunnel URL from above"
echo "2. Update your shopify.app.toml file if needed"
echo "3. Test your extension again"
echo ""
