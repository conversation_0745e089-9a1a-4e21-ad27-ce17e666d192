import { useEffect, useState } from 'react';
import {
  reactExtension,
  useApi,
  AdminAction,
  BlockStack,
  Button,
  Text,
} from '@shopify/ui-extensions-react/admin';

const TARGET = 'admin.order-index.selection-action.render';

export default reactExtension(TARGET, () => <BulkOrderAction />);

function BulkOrderAction() {
  const { close, data } = useApi(TARGET);
  const [orderIds, setOrderIds] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [responseMsg, setResponseMsg] = useState('');

  // Extract all order IDs from selected orders
 useEffect(() => {
  console.log('🔍 Full data object from useApi:', data); // Add this line

  if (data?.selected?.length > 0) {
    const ids = data.selected.map((item) => item.id.split('/').pop());
    setOrderIds(ids);
  }
}, [data]);


  // Send to app/backend
const handleSendToApp = async () => {
  setIsLoading(true);
  setResponseMsg('🔄 Processing orders...');

  try {
    console.log('🚀 Starting order processing for IDs:', orderIds);
    console.log('🌐 Current URL:', window.location.href);

    // Try the new process-orders endpoint first
    console.log('📡 Calling process-orders endpoint...');
    let response = await fetch('/api/process-orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({ orderIds }),
    });

    // If that fails, try the bulk-order-details endpoint as fallback
    if (!response.ok) {
      console.log('⚠️ First endpoint failed, trying fallback...');
      response = await fetch('/resources/bulk-order-details', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({ orderIds }),
      });
    }

    console.log('📨 Response status:', response.status);
    console.log('📨 Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API error:', response.status, errorText);
      throw new Error(`API request failed: ${response.status} ${errorText}`);
    }

    const result = await response.json();
    console.log('📋 API response received:', result);

    // Handle new API format (process-orders endpoint)
    if (result.results) {
      const { successful, failed, summary } = result.results;

      if (summary.successful > 0) {
        setResponseMsg(
          `✅ Success! ${summary.successful} order(s) processed successfully!${
            summary.failed > 0 ? ` ${summary.failed} failed.` : ''
          }`
        );
      } else {
        setResponseMsg('❌ All orders failed to process. Please check the logs.');
      }

      // Log detailed results
      if (successful.length > 0) {
        console.log('✅ Successfully processed orders:', successful.map(r => r.orderId));
      }
      if (failed.length > 0) {
        console.log('❌ Failed orders:', failed);
      }
      return; // Exit early for new API format
    }

    // Handle legacy API format (bulk-order-details endpoint)
    if (!result.orders || result.orders.length === 0) {
      throw new Error('No orders found or unable to fetch order details');
    }

    if (!result.token) {
      throw new Error('API token not found. Please configure your Rushrr API token first.');
    }

    // Send each order to external API (legacy flow)
    let successCount = 0;
    let errorCount = 0;

    for (const order of result.orders) {
      try {
        order.orderReferenceNumber = String(order.order_number);

        const individualPayload = {
          shopifyStoreUrl: `https://${result.shopifyStoreUrl}`,
          orders: [order],
        };

        console.log('📤 Sending order to external API:', order.id);
        const externalRes = await fetch('https://backend.rushr-admin.com/api/orders/create-order', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${result.token}`,
          },
          body: JSON.stringify(individualPayload),
        });

        const externalResult = await externalRes.json();

        if (externalRes.ok) {
          console.log('✅ Successfully sent order:', order.id, 'Response:', externalResult);
          successCount++;
        } else {
          console.error('❌ Failed to send order:', order.id, 'Error:', externalResult);
          errorCount++;
        }
      } catch (orderError) {
        console.error('❌ Error processing individual order:', order.id, orderError);
        errorCount++;
      }
    }

    if (successCount > 0) {
      setResponseMsg(`✅ Success! ${successCount} order(s) sent successfully!${errorCount > 0 ? ` ${errorCount} failed.` : ''}`);
    } else {
      setResponseMsg('❌ All orders failed to send. Please check your configuration.');
    }

  } catch (err) {
    console.error('❌ Error in sending orders:', err);
    setResponseMsg(`❌ Failed to process orders: ${err.message}`);
  } finally {
    setIsLoading(false);
  }
};




  return (
    <AdminAction
      primaryAction={
        <Button disabled={!orderIds.length} loading={isLoading} onPress={handleSendToApp}>
          Send to App
        </Button>
      }
      secondaryAction={<Button onPress={close}>Cancel</Button>}
    >
      <BlockStack spacing="tight">
        <Text fontWeight="bold">Selected Orders</Text>
        {orderIds.length === 0 ? (
          <Text>No orders selected.</Text>
        ) : (
          <Text>Orders: {orderIds.join(', ')}</Text>
        )}
        {responseMsg && <Text>{responseMsg}</Text>}
      </BlockStack>
    </AdminAction>
  );
}
