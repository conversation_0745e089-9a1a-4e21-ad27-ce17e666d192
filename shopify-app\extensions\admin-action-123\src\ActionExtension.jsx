import { useEffect, useState } from 'react';
import {
  reactExtension,
  useApi,
  AdminAction,
  BlockStack,
  Button,
  Text,
} from '@shopify/ui-extensions-react/admin';

const TARGET = 'admin.order-index.selection-action.render';

export default reactExtension(TARGET, () => <BulkOrderAction />);

function BulkOrderAction() {
  const { close, data } = useApi(TARGET);
  const [orderIds, setOrderIds] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [responseMsg, setResponseMsg] = useState('');

  // Extract all order IDs from selected orders
 useEffect(() => {
  console.log('🔍 Full data object from useApi:', data); // Add this line

  if (data?.selected?.length > 0) {
    const ids = data.selected.map((item) => item.id.split('/').pop());
    setOrderIds(ids);
  }
}, [data]);


  // Send to app/backend
const handleSendToApp = async () => {
  setIsLoading(true);
  setResponseMsg('🔄 Processing orders...');

  try {
    console.log('🚀 Starting order processing for IDs:', orderIds);

    // Use the new simplified API endpoint
    const response = await fetch('/api/create-order', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ orderIds }),
    });

    const result = await response.json();
    console.log('📋 API Response:', result);

    if (result.success) {
      const { successful, failed, summary } = result.results;

      if (summary.successful > 0) {
        setResponseMsg(
          `✅ Success! ${summary.successful} order(s) processed successfully.${
            summary.failed > 0 ? ` ${summary.failed} failed.` : ''
          }`
        );
      } else {
        setResponseMsg('❌ All orders failed to process. Please check the logs.');
      }

      // Log detailed results
      if (successful.length > 0) {
        console.log('✅ Successfully processed orders:', successful.map(r => r.orderId));
      }
      if (failed.length > 0) {
        console.log('❌ Failed orders:', failed);
      }
    } else {
      throw new Error(result.error || 'Unknown error occurred');
    }

  } catch (err) {
    console.error('❌ Error in sending orders:', err);
    setResponseMsg(`❌ Failed to process orders: ${err.message}`);
  } finally {
    setIsLoading(false);
  }
};




  return (
    <AdminAction
      primaryAction={
        <Button disabled={!orderIds.length} loading={isLoading} onPress={handleSendToApp}>
          Send to App
        </Button>
      }
      secondaryAction={<Button onPress={close}>Cancel</Button>}
    >
      <BlockStack spacing="tight">
        <Text fontWeight="bold">Selected Orders</Text>
        {orderIds.length === 0 ? (
          <Text>No orders selected.</Text>
        ) : (
          <Text>Orders: {orderIds.join(', ')}</Text>
        )}
        {responseMsg && <Text>{responseMsg}</Text>}
      </BlockStack>
    </AdminAction>
  );
}
