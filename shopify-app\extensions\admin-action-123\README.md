# Action Extension

Admin action extensions enable app developers to build custom functionality into the context of Shopify Admin. These extensions surface as launchable menu actions that open interactive modals at various extension targets and enhance the merchant experience. Developers can build the content of these extensions using Shopify's UI Extension components for Admin.

Learn more about Admin action extensions in Shopify’s [developer documentation](https://shopify.dev/docs/apps/admin/admin-actions-and-blocks).
