# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "7fc2a82dd93bbe0fe0ecb85a81e21774"
name = "logistic-app-newone"
handle = "logistic-app-newone"
application_url = "https://sunset-saudi-publisher-chorus.trycloudflare.com"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-07"
[[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_orders,write_products"

[auth]
redirect_urls = ["https://sunset-saudi-publisher-chorus.trycloudflare.com/auth/callback", "https://sunset-saudi-publisher-chorus.trycloudflare.com/auth/shopify/callback", "https://sunset-saudi-publisher-chorus.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false
