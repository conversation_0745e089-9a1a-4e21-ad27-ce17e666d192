import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { getToken } from "../utils/tokenStorage";
import axios from "axios";

// Enhanced CORS headers for extension compatibility
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With, Accept",
  "Access-Control-Allow-Credentials": "true",
  "Access-Control-Max-Age": "86400",
};

// Handle CORS preflight
export const loader = ({ request }) => {
  console.log("🔄 CORS preflight request received for process-orders");
  return new Response(null, {
    status: 204,
    headers: corsHeaders,
  });
};

// Main order processing endpoint
export const action = async ({ request }) => {
  console.log("📨 Process orders request received");
  console.log("Method:", request.method);
  console.log("URL:", request.url);
  console.log("Headers:", Object.fromEntries(request.headers.entries()));

  if (request.method !== "POST") {
    console.log("❌ Method not allowed:", request.method);
    return json(
      { error: `Method ${request.method} not allowed` },
      { status: 405, headers: corsHeaders }
    );
  }

  try {
    // Parse request body first
    let requestBody;
    try {
      requestBody = await request.json();
      console.log("📦 Request body:", requestBody);
    } catch (parseError) {
      console.error("❌ Failed to parse request body:", parseError);
      return json(
        {
          success: false,
          error: "Invalid JSON in request body",
          details: parseError.message
        },
        { status: 400, headers: corsHeaders }
      );
    }
    
    const { orderIds } = requestBody;
    
    if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
      return json(
        { 
          success: false,
          error: "Invalid or missing orderIds. Expected array of order IDs." 
        },
        { status: 400, headers: corsHeaders }
      );
    }

    console.log("🔐 Authenticating request...");
    
    // Authenticate the session
    const { session } = await authenticate.admin(request);
    const { shop, accessToken } = session;
    
    console.log("✅ Authentication successful for shop:", shop);
    
    // Get Rushrr API token
    const rushrrApiToken = getToken(shop);
    
    if (!rushrrApiToken) {
      console.log("❌ No Rushrr API token found");
      return json(
        { 
          success: false,
          error: "Rushrr API token not found. Please configure your token in the app settings." 
        },
        { status: 400, headers: corsHeaders }
      );
    }

    console.log("🔑 Token found, processing orders:", orderIds);

    // Process each order
    const results = [];
    const errors = [];

    for (const orderId of orderIds) {
      try {
        console.log(`📥 Fetching order ${orderId} from Shopify...`);
        
        // Fetch order from Shopify
        const shopifyResponse = await axios.get(
          `https://${shop}/admin/api/2024-07/orders/${orderId}.json`,
          {
            headers: {
              "X-Shopify-Access-Token": accessToken,
              "Content-Type": "application/json",
            },
            timeout: 10000,
          }
        );

        const order = shopifyResponse.data.order;
        
        if (!order) {
          throw new Error(`Order ${orderId} not found`);
        }

        console.log(`✅ Order ${orderId} fetched successfully`);

        // Prepare payload for external API
        const orderPayload = {
          shopifyStoreUrl: `https://${shop}`,
          orders: [{
            ...order,
            orderReferenceNumber: String(order.order_number),
          }],
        };

        console.log(`📤 Sending order ${orderId} to external API...`);
        
        // Send to external API
        const externalResponse = await axios.post(
          'https://backend.rushr-admin.com/api/orders/create-order',
          orderPayload,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${rushrrApiToken}`,
            },
            timeout: 15000,
          }
        );

        console.log(`✅ Order ${orderId} sent successfully`);
        results.push({
          orderId,
          success: true,
          orderNumber: order.order_number,
          externalResponse: externalResponse.data,
        });

      } catch (error) {
        console.error(`❌ Error processing order ${orderId}:`, error.message);
        errors.push({
          orderId,
          error: error.message,
          details: error.response?.data || null,
        });
      }
    }

    const successCount = results.length;
    const errorCount = errors.length;
    
    console.log(`📊 Processing complete: ${successCount} success, ${errorCount} errors`);

    return json(
      {
        success: successCount > 0,
        message: `Processed ${orderIds.length} orders: ${successCount} successful, ${errorCount} failed`,
        results: {
          successful: results,
          failed: errors,
          summary: {
            total: orderIds.length,
            successful: successCount,
            failed: errorCount,
          }
        }
      },
      { 
        status: 200,
        headers: corsHeaders 
      }
    );

  } catch (err) {
    console.error("❌ Unexpected error:", err);
    return json(
      { 
        success: false,
        error: "Internal server error",
        details: err.message 
      },
      { status: 500, headers: corsHeaders }
    );
  }
};
